* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light theme colors */
    --primary-color: #f87171;
    --secondary-color: #fca5a5;
    --accent-color: #f87171;
    --bg-color: #ffffff;
    --card-bg: rgba(255, 255, 255, 0.9);
    --text-color: #333333;
    --text-muted: #666666;
    --gradient-1: linear-gradient(135deg, #f87171 0%, #fca5a5 100%);
    --gradient-2: linear-gradient(135deg, #fca5a5 0%, #fecaca 100%);
    --glass-bg: rgba(255, 255, 255, 0.8);
    --glass-border: rgba(0, 0, 0, 0.1);
    --nav-bg: rgba(255, 255, 255, 0.95);
    --footer-bg: rgba(255, 255, 255, 0.9);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --hover-shadow: rgba(248, 113, 113, 0.2);
}

[data-theme="dark"] {
    /* Dark theme colors */
    --primary-color: #dc2626;
    --secondary-color: #b91c1c;
    --accent-color: #dc2626;
    --bg-color: #0f0f23;
    --card-bg: rgba(255, 255, 255, 0.1);
    --text-color: #ffffff;
    --text-muted: #a0a0a0;
    --gradient-1: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    --gradient-2: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --nav-bg: rgba(15, 15, 35, 0.9);
    --footer-bg: rgba(15, 15, 35, 0.8);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --hover-shadow: rgba(220, 38, 38, 0.4);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Animated Background */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--bg-color);
    transition: background-color 0.3s ease;
}

.bg-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: bgFloat 20s ease-in-out infinite;
}

@keyframes bgFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
    33% { transform: translateY(-30px) rotate(1deg) scale(1.1); }
    66% { transform: translateY(30px) rotate(-1deg) scale(0.9); }
}

/* Navigation */
nav {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 1rem 2rem;
    background: var(--nav-bg);
    backdrop-filter: blur(20px);
    z-index: 1000;
    border-bottom: 1px solid var(--glass-border);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--accent-color);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-2);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Theme Toggle */
.theme-toggle {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-color);
    backdrop-filter: blur(10px);
}

.theme-toggle:hover {
    background: var(--accent-color);
    color: white;
    transform: scale(1.1);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
    position: relative;
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1rem;
    color: var(--text-color);
    animation: fadeInUp 1s ease;
}

.hero-content p {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    margin-bottom: 2rem;
    color: var(--text-muted);
    animation: fadeInUp 1s ease 0.2s both;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.4s both;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--gradient-1);
    color: white;
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-color);
    border: 2px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.btn-coffee {
    background: linear-gradient(135deg, #FFDD00 0%, #FBB040 100%);
    color: #8B4513;
    border: 2px solid #FBB040;
    font-weight: 700;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px var(--hover-shadow);
}

.btn-coffee:hover {
    background: linear-gradient(135deg, #FBB040 0%, #FF8C00 100%);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(251, 176, 64, 0.4);
}

/* Stats Section */
.stats {
    padding: 4rem 2rem;
    text-align: center;
}

.stats-grid {
    max-width: 1000px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: var(--glass-bg);
    padding: 2rem;
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px var(--shadow-color);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    background: var(--gradient-2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: var(--text-muted);
    margin-top: 0.5rem;
}

/* Projects Section */
.projects {
    padding: 6rem 2rem;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 1rem;
    background: var(--gradient-1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    color: var(--text-muted);
    font-size: 1.2rem;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: var(--glass-bg);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-1);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.project-card:hover::before {
    transform: scaleX(1);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px var(--shadow-color);
}

.project-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.project-title {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.project-description {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.project-features {
    margin-bottom: 1.5rem;
}

.project-features ul {
    list-style: none;
    padding: 0;
}

.project-features li {
    padding: 0.25rem 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.project-features li::before {
    content: '✓';
    color: var(--accent-color);
    margin-right: 0.5rem;
    font-weight: bold;
}

.project-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.project-link {
    padding: 8px 16px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 25px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-link:hover {
    background: var(--gradient-1);
    color: white;
    transform: translateY(-2px);
}

.tech-stack {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: rgba(248, 113, 113, 0.2);
    color: var(--accent-color);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(248, 113, 113, 0.3);
}

[data-theme="dark"] .tech-tag {
    background: rgba(220, 38, 38, 0.2);
    border: 1px solid rgba(220, 38, 38, 0.3);
}

/* Developer Section */
.developer {
    padding: 6rem 2rem;
    background: var(--glass-bg);
}

.developer-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.developer-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: var(--glass-bg);
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    /* border: 4px solid var(--glass-border); */
    overflow: hidden;
}

.avatar-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.developer-name {
    font-size: 2rem;
    margin-bottom: 1rem;
    background: var(--gradient-1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.developer-bio {
    color: var(--text-muted);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: var(--gradient-1);
    color: white;
    transform: translateY(-3px);
}

/* Special styling for Buy Me a Coffee */
.buy-me-coffee {
    background: linear-gradient(135deg, #FFDD00 0%, #FBB040 100%);
    color: #8B4513;
    border: 1px solid #FBB040;
}

.buy-me-coffee:hover {
    background: linear-gradient(135deg, #FBB040 0%, #FF8C00 100%);
    color: white;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(251, 176, 64, 0.4);
}

/* Footer */
footer {
    padding: 2rem;
    text-align: center;
    border-top: 1px solid var(--glass-border);
    background: var(--footer-bg);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    nav {
        padding: 1rem;
    }

    .hero {
        padding: 1rem;
    }

    .projects {
        padding: 4rem 1rem;
    }

    .developer {
        padding: 4rem 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
