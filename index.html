<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GarudadevDataServices - Innovative Data Solutions & Open Source Projects</title>
    <meta name="description" content="Explore innovative data visualization projects, geographic tools, APIs, and open source contributions by <PERSON><PERSON><PERSON> - GarudadevDataServices">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/svg+xml" href="garuda-icon.svg">
</head>
<body>
    <div class="bg-animation"></div>

    <nav>
        <div class="nav-container">
            <div class="logo">
                <img src="garuda-icon.svg" alt="Garuda Icon" class="logo-icon">
                GarudadevDataServices
            </div>
            <div class="nav-right">
                <ul class="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#projects">Projects</a></li>
                    <li><a href="#developer">Developer</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </nav>

    <section class="hero" id="home">
        <div class="hero-content">
            <h1>GarudadevDataServices</h1>
            <p>Innovative Data Visualization, Geographic Tools & Open Source Solutions</p>
            <div class="cta-buttons">
                <a href="#projects" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Explore Projects
                </a>
                <a href="https://akuladatta.github.io" class="btn btn-secondary" target="_blank">
                    <i class="fas fa-user"></i>
                    My Portfolio
                </a>
            </div>
        </div>
    </section>

    <section class="stats fade-in">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">13+</div>
                <div class="stat-label">Active Projects</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">10K+</div>
                <div class="stat-label">App Downloads</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">Open Source</div>
            </div>
        </div>
    </section>

    <section class="projects" id="projects">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Featured Projects</h2>
                <p class="section-subtitle">Explore my collection of data visualization tools, APIs, and applications</p>
            </div>

            <div class="projects-grid">
                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-map"></i>
                    </div>
                    <h3 class="project-title">MapExpo</h3>
                    <p class="project-description">Interactive platform showcasing various map visualizations and geographic data insights focused on India.</p>
                    <div class="project-features">
                        <ul>
                            <li>Interactive map interfaces</li>
                            <li>Multiple data visualizations</li>
                            <li>Geographic data analysis</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://garudadevdataservices.github.io/mapexpo/" class="project-link" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            Live Demo
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">HTML</span>
                        <span class="tech-tag">CSS</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                </div>

                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="project-title">INDIAN-SHAPEFILES</h3>
                    <p class="project-description">Comprehensive collection of high-quality GeoJSON files for Indian geographic boundaries and administrative divisions.</p>
                    <div class="project-features">
                        <ul>
                            <li>State & district boundaries</li>
                            <li>Metropolitan city wards</li>
                            <li>Political constituencies</li>
                            <li>Transportation networks</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://github.com/datta07/INDIAN-SHAPEFILES" class="project-link" target="_blank">
                            <i class="fab fa-github"></i>
                            GitHub
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">GeoJSON</span>
                        <span class="tech-tag">Mapshaper</span>
                        <span class="tech-tag">GIS</span>
                    </div>
                </div>

                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3 class="project-title">Language Map</h3>
                    <p class="project-description">Interactive visualization tool for exploring language distribution patterns across different regions of India.</p>
                    <div class="project-features">
                        <ul>
                            <li>Language selection interface</li>
                            <li>Interactive choropleth maps</li>
                            <li>Regional data insights</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://garudadevdataservices.github.io/language_map/" class="project-link" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            Live Demo
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">HTML</span>
                        <span class="tech-tag">CSS</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                </div>

                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3 class="project-title">Where They All From</h3>
                    <p class="project-description">Data visualization project mapping birthplaces of famous Indian personalities across various categories and professions.</p>
                    <div class="project-features">
                        <ul>
                            <li>Interactive Folium maps</li>
                            <li>Statistical visualizations</li>
                            <li>12 personality categories</li>
                            <li>Wikipedia data integration</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://github.com/datta07/Where-they-all-from" class="project-link" target="_blank">
                            <i class="fab fa-github"></i>
                            GitHub
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">Python</span>
                        <span class="tech-tag">Folium</span>
                        <span class="tech-tag">Pandas</span>
                        <span class="tech-tag">Matplotlib</span>
                    </div>
                </div>

                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-tv"></i>
                    </div>
                    <h3 class="project-title">TV Channel Schedule API</h3>
                    <p class="project-description">RESTful API providing comprehensive access to Indian television channel schedules, categories, and movie listings.</p>
                    <div class="project-features">
                        <ul>
                            <li>Channel search & categories</li>
                            <li>Daily schedule retrieval</li>
                            <li>Movie listings by language</li>
                            <li>Heroku deployment ready</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://github.com/datta07/Tv-Channel-Schedule-API" class="project-link" target="_blank">
                            <i class="fab fa-github"></i>
                            GitHub
                        </a>
                        <a href="https://rapidapi.com/Garuda07/api/indian-tv-schedule" class="project-link" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            API Demo
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">Python</span>
                        <span class="tech-tag">Flask</span>
                        <span class="tech-tag">REST API</span>
                    </div>
                </div>

                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="project-title">GarudaDevBot</h3>
                    <p class="project-description">Telegram bot for searching and downloading songs from Jio Saavn with multiple quality options and lyrics extraction.</p>
                    <div class="project-features">
                        <ul>
                            <li>Song search by name/movie</li>
                            <li>96kbps & 320kbps downloads</li>
                            <li>Lyrics extraction</li>
                            <li>Telegram integration</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://t.me/garudaDevBot" class="project-link" target="_blank">
                            <i class="fab fa-telegram"></i>
                            Try Bot
                        </a>
                        <a href="https://github.com/datta07/Jio-Savvn-Song-Downloader" class="project-link" target="_blank">
                            <i class="fab fa-github"></i>
                            Source
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">Python</span>
                        <span class="tech-tag">Telegram Bot API</span>
                        <span class="tech-tag">Web Scraping</span>
                    </div>
                </div>

                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="project-title">Bhagavad Gita Telugu</h3>
                    <p class="project-description">Android application providing access to the sacred Bhagavad Gita in Telugu language with over 10K+ downloads.</p>
                    <div class="project-features">
                        <ul>
                            <li>Complete Telugu translation</li>
                            <li>User-friendly interface</li>
                            <li>Offline access</li>
                            <li>10K+ downloads</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://play.google.com/store/apps/details?id=com.bhagawathgita.telugu" class="project-link" target="_blank">
                            <i class="fab fa-google-play"></i>
                            Play Store
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">Android</span>
                        <span class="tech-tag">Java/Kotlin</span>
                        <span class="tech-tag">Android Studio</span>
                    </div>
                </div>

                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h3 class="project-title">Magic Square</h3>
                    <p class="project-description">Generate personalized magic squares based on any date, inspired by the mathematical genius Srinivasa Ramanujan.</p>
                    <div class="project-features">
                        <ul>
                            <li>Date-based generation</li>
                            <li>Beautiful user interface</li>
                            <li>Ramanujan inspired</li>
                            <li>Mathematical elegance</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://github.com/datta07/MAGIC-SQUARE" class="project-link" target="_blank">
                            <i class="fab fa-github"></i>
                            GitHub
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">C</span>
                        <span class="tech-tag">Mathematics</span>
                        <span class="tech-tag">Algorithm</span>
                    </div>
                </div>

                <div class="project-card fade-in">
                    <div class="project-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="project-title">Panchang Now</h3>
                    <p class="project-description">Hindu calendar tool providing panchang information including auspicious timings, festivals, and astrological data.</p>
                    <div class="project-features">
                        <ul>
                            <li>Hindu calendar integration</li>
                            <li>Auspicious timings</li>
                            <li>Festival information</li>
                            <li>Under active development</li>
                        </ul>
                    </div>
                    <div class="project-links">
                        <a href="https://garudadevdataservices.github.io/panchang_now/" class="project-link" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            Preview
                        </a>
                    </div>
                    <div class="tech-stack">
                        <span class="tech-tag">JavaScript</span>
                        <span class="tech-tag">HTML/CSS</span>
                        <span class="tech-tag">Web API</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="developer" id="developer">
        <div class="developer-content fade-in">
            <div class="developer-avatar">
                <img src="garuda-icon.svg" alt="Garuda Icon" class="avatar-icon">
            </div>
            <h2 class="developer-name">Akula Guru Datta</h2>
            <p class="developer-bio">
                Passionate about data visualization, geographic information systems (GIS), and building impactful open-source projects.
                I strive to create intuitive tools and applications that leverage data to solve real-world problems and contribute to the developer community.
                My focus is often on bringing complex data to life through interactive maps and user-friendly interfaces.
            </p>
            <div class="social-links">
                <a href="https://github.com/datta07" class="social-link" target="_blank" aria-label="GitHub Profile">
                    <i class="fab fa-github fa-2x"></i>
                </a>
                <a href="https://akuladatta.github.io" class="social-link" target="_blank" aria-label="Personal Portfolio">
                    <i class="fas fa-globe fa-2x"></i>
                </a>
                <!-- You can add more social links here -->
                <!-- <a href="#" class="social-link" target="_blank" aria-label="LinkedIn Profile">
                    <i class="fab fa-linkedin-in fa-2x"></i>
                </a> -->
            </div>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <p>© 2024 GarudadevDataServices. All rights reserved.</p>
            <p>Built with ❤️ for Open Source.</p>
            <p>Contact: <a href="mailto:<EMAIL>" style="color: var(--accent-color); text-decoration: none;"><EMAIL></a></p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>